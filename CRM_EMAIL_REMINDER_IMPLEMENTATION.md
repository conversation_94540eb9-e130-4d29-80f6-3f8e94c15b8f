# PRESTIGE Car Wash - Automatic Email Reminder System Implementation Guide

## Overview
This document provides complete implementation details for setting up automatic email reminders for confirmed bookings, including no-show penalty information. The system sends reminders at 24 hours, 2 hours, and 30 minutes before appointments.

## System Architecture

### 1. API Endpoints Created
- `GET /api/bookings/reminders` - Find bookings needing reminders
- `POST /api/bookings/reminders` - Send specific reminder
- `GET /api/cron/booking-reminders` - Automated cron job endpoint

### 2. Reminder Schedule
- **24 Hours Before**: Appointment confirmation + no-show policy
- **2 Hours Before**: "Get ready" reminder + last chance to reschedule
- **30 Minutes Before**: "Time to head out" + final warning

## CRM Integration Requirements

### Environment Variables
```bash
# --- SMTP Login Credentials ---
# Using <PERSON><PERSON>'s real user account to authorize sending.
EMAIL_USER="<EMAIL>"
EMAIL_PASS="bPgd8AAfHvEB"
CRON_SECRET=CW9x7mK2pL8nR4vB6qE3tY1uI5oP0aS9dF7gH2jK4lM6nQ8rT3wE5yU7iO9pA1sD
NEXT_PUBLIC_SITE_URL=https://prestigebyekhaya.com
```

### Cron Job Configuration
```json
{
  "endpoint": "https://prestigebyekhaya.com/api/cron/booking-reminders",
  "method": "GET",
  "schedule": "*/15 * * * *",
  "headers": {
    "Authorization": "Bearer CW9x7mK2pL8nR4vB6qE3tY1uI5oP0aS9dF7gH2jK4lM6nQ8rT3wE5yU7iO9pA1sD",
    "Content-Type": "application/json"
  }
}
```

### Vercel Cron Setup
File: `vercel.json`
```json
{
  "crons": [
    {
      "path": "/api/cron/booking-reminders",
      "schedule": "*/15 * * * *"
    }
  ]
}
```

## No-Show Policy Information

### Policy Details
- **Free Cancellation**: Up to 4 hours before appointment
- **Late Cancellation Fee**: R50 (less than 4 hours notice)
- **No-Show Penalty**: R100 (failure to attend without notice)
- **Account Restrictions**: Multiple no-shows may result in booking limitations

### Policy Text for Emails
```html
<div class="penalty-info">
  <h3>⚠️ Important Reminder - No-Show Policy</h3>
  <p><strong>Please note our no-show and cancellation policy:</strong></p>
  <ul>
    <li>🕐 <strong>Free cancellation:</strong> Up to 4 hours before your appointment</li>
    <li>💰 <strong>Late cancellation fee:</strong> R50 if cancelled less than 4 hours before</li>
    <li>❌ <strong>No-show penalty:</strong> R100 charge if you don't show up or cancel</li>
    <li>🚫 <strong>Account restriction:</strong> Multiple no-shows may result in booking restrictions</li>
  </ul>
  <p style="color: #d97706;"><strong>We understand things happen! Just let us know as early as possible.</strong></p>
</div>
```

## Email Templates

### 24-Hour Reminder
- **Subject**: `⏰ Tomorrow's Appointment Reminder - PRESTIGE Car Wash`
- **Content**: Full appointment details + no-show policy
- **CTA**: View Dashboard, Reschedule buttons
- **Color Theme**: Green (#10b981)

### 2-Hour Reminder  
- **Subject**: `🚗 Get Ready! Your appointment is in 2 hours - PRESTIGE Car Wash`
- **Content**: "Get ready" message + last chance warning
- **CTA**: View Dashboard button
- **Color Theme**: Orange (#f59e0b)

### 30-Minute Reminder
- **Subject**: `🏃‍♂️ Time to Head Out! Your appointment is in 30 minutes - PRESTIGE Car Wash`
- **Content**: "Time to head out" + final penalty warning
- **CTA**: Call button for late arrivals
- **Color Theme**: Red (#ef4444)

## API Usage Examples

### Check Pending Reminders
```bash
curl -X GET "https://prestigebyekhaya.com/api/bookings/reminders"
```

**Response:**
```json
{
  "success": true,
  "count": 3,
  "pendingReminders": [
    {
      "bookingId": "booking-123",
      "type": "24_hours",
      "customerEmail": "<EMAIL>",
      "customerName": "John Doe",
      "appointmentDate": "Tuesday, January 9, 2024",
      "appointmentTime": "10:00",
      "serviceName": "Premium Wash",
      "vehicleInfo": "BMW X3",
      "plateNumber": "CA123GP",
      "hoursUntil": 23.5
    }
  ]
}
```

### Send Specific Reminder
```bash
curl -X POST "https://prestigebyekhaya.com/api/bookings/reminders" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "24_hours",
    "bookingId": "booking-123"
  }'
```

### Test Mode (No Emails Sent)
```bash
curl -X POST "https://prestigebyekhaya.com/api/cron/booking-reminders" \
  -H "Content-Type: application/json" \
  -d '{"testMode": true}'
```

## CRM Automation Workflow

### 1. Monitor Bookings
- Track all confirmed bookings in CRM
- Monitor booking status changes
- Identify upcoming appointments

### 2. Set Time-Based Triggers
```
IF booking.status = "CONFIRMED" AND 
   booking.appointmentTime - current_time = 24_hours
THEN call_reminder_api("24_hours", booking.id)

IF booking.status = "CONFIRMED" AND 
   booking.appointmentTime - current_time = 2_hours
THEN call_reminder_api("2_hours", booking.id)

IF booking.status = "CONFIRMED" AND 
   booking.appointmentTime - current_time = 30_minutes
THEN call_reminder_api("30_minutes", booking.id)
```

### 3. Handle No-Shows
- Mark booking as "NO_SHOW" if customer doesn't arrive
- Apply R100 penalty charge
- Send follow-up email with penalty notice
- Track no-show count for account restrictions

## Implementation Checklist

### Backend Setup
- [ ] Create `/api/bookings/reminders` endpoint
- [ ] Update existing cron job endpoint
- [ ] Configure email transporter (SMTP)
- [ ] Set up environment variables
- [ ] Deploy Vercel cron configuration

### CRM Configuration
- [ ] Set up 15-minute cron job to call reminder endpoint
- [ ] Configure time-based triggers for each reminder type
- [ ] Implement no-show tracking and penalty system
- [ ] Set up webhook endpoints for booking status updates
- [ ] Test reminder system with sample bookings

### Email Setup
- [ ] Configure SMTP credentials
- [ ] Test email delivery
- [ ] Verify email templates render correctly
- [ ] Set up email tracking/analytics
- [ ] Configure bounce/complaint handling

### Testing
- [ ] Test 24-hour reminder flow
- [ ] Test 2-hour reminder flow  
- [ ] Test 30-minute reminder flow
- [ ] Verify no-show policy information displays
- [ ] Test with different time zones
- [ ] Validate email deliverability

## Monitoring & Analytics

### Key Metrics to Track
- Reminder emails sent per day
- Email open rates by reminder type
- Click-through rates on CTA buttons
- No-show rate before/after implementation
- Cancellation rate by reminder timing

### Logging
All reminder activities are logged to the database notifications table:
```sql
INSERT INTO notifications (userId, title, message, type, createdAt)
VALUES (user_id, 'Reminder Sent', 'Details...', 'REMINDER', NOW())
```

## Troubleshooting

### Common Issues
1. **Emails not sending**: Check SMTP credentials and email service limits
2. **Wrong timing**: Verify server timezone matches business timezone  
3. **Duplicate reminders**: Implement reminder tracking to prevent duplicates
4. **Missing bookings**: Ensure booking status is "CONFIRMED" in database

### Debug Endpoints
- Test mode: `POST /api/cron/booking-reminders` with `{"testMode": true}`
- Check pending: `GET /api/bookings/reminders`
- Manual trigger: `POST /api/bookings/reminders` with specific booking ID

## Contact Information
- **Business Hours**: Mon-Fri: 8AM-6PM, Sat: 8AM-5PM, Sun: 9AM-2PM
- **Phone**: +27 78 613 2969
- **Email**: <EMAIL>
- **Address**: 1 Piers Road, Cape Town, Western Cape, 7800, South Africa
