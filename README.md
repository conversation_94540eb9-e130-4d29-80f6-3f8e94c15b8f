# Ekhaya Car Wash CRM System

A comprehensive, mobile-first Customer Relationship Management system designed specifically for car wash operations. Built with Next.js, TypeScript, and Prisma.

## 🚀 Features

### Core CRM Functionality
- **Customer Management** - Complete profiles with service history, vehicles, and loyalty tracking
- **Live Service Tracking** - Real-time monitoring of car wash workflow steps
- **Photo Documentation** - Before/during/after service photography with approval workflow
- **Staff Management** - Assignment, performance tracking, and task management
- **Mobile-First Design** - Optimized for tablets and smartphones used by staff

### Advanced Features
- **Service Workflow Engine** - 6-step car wash process tracking
- **Real-time Dashboard** - Live stats, bookings, revenue, and performance metrics
- **Quality Control** - Photo approval system and service verification
- **Multi-bay Management** - Track services across multiple wash bays
- **Customer Journey** - Complete service history and preferences

### Security & Administration
- **Role-based Access Control** - Super Admin, Admin, Staff, Viewer roles
- **IP Allowlisting** - Restrict access by IP address/ranges
- **Secure Authentication** - Next-auth with credential-based login
- **Audit Logging** - Track all admin actions and changes

## 🛠️ Quick Setup

### Prerequisites
- Node.js 18+ 
- PostgreSQL database (Neon recommended)
- Admin email for initial setup

### Installation

1. **Clone and Install**
```bash
cd ekhaya-crm
npm install
```

2. **Environment Setup**
```bash
cp .env.example .env
```

Edit `.env` with your database URL and settings:
```env
DATABASE_URL="your-neon-postgresql-url"
NEXTAUTH_SECRET="your-super-secret-key"
NEXTAUTH_URL="http://localhost:3001"
ADMIN_EMAIL="<EMAIL>"
```

3. **Database Setup**
```bash
npx prisma db push
npx prisma generate
```

4. **Create First Admin User**
```bash
npm run create-admin
```

5. **Start Development**
```bash
npm run dev
```

The CRM will be available at `http://localhost:3001`

## 📱 Mobile Usage

This CRM is designed to be used on tablets and smartphones by car wash staff:

### Staff Workflow
1. **Login** - Secure authentication for each staff member
2. **Dashboard** - View assigned services and daily tasks
3. **Service Tracking** - Step-by-step workflow completion
4. **Photo Documentation** - Take before/during/after photos
5. **Quality Control** - Mark services complete with notes

### Management Features
- **Real-time Monitoring** - Watch all active services
- **Staff Performance** - Track completion times and quality
- **Customer Management** - Complete relationship history
- **Analytics** - Revenue, efficiency, and customer insights

## 🔐 Security Features

### Access Control
- **IP Allowlisting** - Set `ALLOWED_ADMIN_IPS` in environment
- **Role Hierarchy** - Different access levels for different roles
- **Session Management** - Secure session handling with NextAuth

### Production Security
- **HTTPS Required** - Always use SSL in production
- **Database Security** - Use connection pooling and SSL
- **Environment Variables** - Never commit secrets to version control

## 🏗️ Architecture

### Tech Stack
- **Frontend**: Next.js 14, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API routes, Prisma ORM
- **Database**: PostgreSQL (Neon Cloud recommended)
- **Authentication**: NextAuth.js with credentials provider
- **UI Components**: Radix UI primitives with custom styling

### Database Schema
- **AdminUser** - CRM system users
- **User** - Car wash customers (shared with main website)
- **Booking** - Service appointments and tracking
- **Payment** - Transaction records
- **Service** - Available car wash services
- **Vehicle** - Customer vehicle information

## 📊 Key Modules

### 1. Dashboard (`/dashboard`)
- Live service statistics
- Today's bookings and progress
- Quick action buttons
- Staff performance overview

### 2. Customer Management (`/customers`)
- Search and filter customers
- Membership tier management
- Service history and preferences
- Vehicle registration

### 3. Live Tracking (`/tracking`)
- Real-time service monitoring
- Step-by-step workflow progress
- Staff assignment and bay management
- Photo documentation integration

### 4. Photo Gallery (`/photos`)
- Service documentation photos
- Before/during/after comparisons
- Quality control and approval
- Customer satisfaction evidence

### 5. Service Management (`/services`)
- Service catalog management
- Pricing and duration settings
- Add-on and upgrade options

## 🚀 Deployment

### Production Deployment
1. **Build the Application**
```bash
npm run build
```

2. **Environment Variables** (Production)
```env
DATABASE_URL="your-production-db-url"
NEXTAUTH_SECRET="your-production-secret"
NEXTAUTH_URL="https://your-crm-domain.com"
NODE_ENV="production"
ALLOWED_ADMIN_IPS="your-office-ips"
```

3. **Deploy to Vercel** (Recommended)
```bash
vercel --prod
```

### Custom Domain Setup
- Configure custom subdomain (e.g., `crm.ekhayacarwash.com`)
- Set up SSL certificates
- Configure IP restrictions if needed

## 🔧 Customization

### Adding New Service Steps
Edit the `serviceSteps` array in `/app/tracking/page.tsx`

### Custom Branding
- Update logo and colors in `/app/globals.css`
- Modify company name in sidebar component
- Customize email templates and notifications

### Additional Features
The system is built modularly - easily add:
- SMS notifications
- Advanced analytics
- Customer feedback system
- Inventory management
- Staff scheduling

## 📞 Support

For technical support or feature requests:
- Check the GitHub issues
- Review the documentation
- Contact the development team

## 📄 License

Proprietary software for Ekhaya Car Wash operations.

---

**Built with ❤️ for efficient car wash management**