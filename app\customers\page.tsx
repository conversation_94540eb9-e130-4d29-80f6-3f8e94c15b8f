'use client';

import { useState } from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Search, 
  Plus, 
  Filter, 
  MoreHorizontal,
  Phone,
  Mail,
  Car,
  Calendar,
  Star,
  MapPin,
  Crown
} from 'lucide-react';
import { formatCurrency, formatDate, getInitials, timeAgo } from '@/lib/utils';

// Mock customer data - will be replaced with real API
const customers = [
  {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+27 82 123 4567',
    address: 'Sandton, Johannesburg',
    loyaltyPoints: 1250,
    totalSpent: 8500,
    lastVisit: new Date('2024-01-10'),
    membershipTier: 'PREMIUM',
    totalBookings: 23,
    vehicles: [
      { make: 'BMW', model: 'X5', year: 2022, color: 'Black' },
      { make: 'Audi', model: 'A4', year: 2020, color: 'White' }
    ],
    status: 'ACTIVE'
  },
  {
    id: 2,
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+27 83 987 6543',
    address: 'Rosebank, Johannesburg',
    loyaltyPoints: 850,
    totalSpent: 4200,
    lastVisit: new Date('2024-01-08'),
    membershipTier: 'BASIC',
    totalBookings: 12,
    vehicles: [
      { make: 'Toyota', model: 'Corolla', year: 2021, color: 'Silver' }
    ],
    status: 'ACTIVE'
  },
  {
    id: 3,
    name: 'Mike Wilson',
    email: '<EMAIL>',
    phone: '+27 84 555 7890',
    address: 'Centurion, Pretoria',
    loyaltyPoints: 2100,
    totalSpent: 12400,
    lastVisit: new Date('2024-01-12'),
    membershipTier: 'VIP',
    totalBookings: 34,
    vehicles: [
      { make: 'Mercedes', model: 'C-Class', year: 2023, color: 'Blue' },
      { make: 'BMW', model: '320i', year: 2019, color: 'Red' }
    ],
    status: 'ACTIVE'
  },
  {
    id: 4,
    name: 'Lisa Brown',
    email: '<EMAIL>',
    phone: '+27 81 234 5678',
    address: 'Umhlanga, Durban',
    loyaltyPoints: 350,
    totalSpent: 1800,
    lastVisit: new Date('2023-12-20'),
    membershipTier: 'BASIC',
    totalBookings: 6,
    vehicles: [
      { make: 'Honda', model: 'Civic', year: 2018, color: 'White' }
    ],
    status: 'INACTIVE'
  }
];

function getMembershipColor(tier: string) {
  switch (tier) {
    case 'VIP': return 'text-purple-600 bg-purple-50 border-purple-200';
    case 'PREMIUM': return 'text-blue-600 bg-blue-50 border-blue-200';
    case 'BASIC': return 'text-green-600 bg-green-50 border-green-200';
    default: return 'text-gray-600 bg-gray-50 border-gray-200';
  }
}

function getMembershipIcon(tier: string) {
  switch (tier) {
    case 'VIP': return <Crown className="w-3 h-3" />;
    case 'PREMIUM': return <Star className="w-3 h-3" />;
    default: return null;
  }
}

export default function CustomersPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('ALL');

  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         customer.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         customer.phone.includes(searchQuery);
    
    const matchesFilter = selectedFilter === 'ALL' || 
                         (selectedFilter === 'ACTIVE' && customer.status === 'ACTIVE') ||
                         (selectedFilter === 'VIP' && customer.membershipTier === 'VIP') ||
                         (selectedFilter === 'PREMIUM' && customer.membershipTier === 'PREMIUM');

    return matchesSearch && matchesFilter;
  });

  return (
    <DashboardLayout title="Customer Management" subtitle="Manage customer relationships and service history">
      <div className="p-6 space-y-6">
        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row gap-4 justify-between">
          <div className="flex flex-1 gap-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Search customers..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline" size="sm">
              <Filter className="w-4 h-4 mr-2" />
              Filter
            </Button>
          </div>
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            Add Customer
          </Button>
        </div>

        {/* Filter Tabs */}
        <div className="flex space-x-2 border-b">
          {['ALL', 'ACTIVE', 'VIP', 'PREMIUM'].map((filter) => (
            <button
              key={filter}
              onClick={() => setSelectedFilter(filter)}
              className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                selectedFilter === filter
                  ? 'border-primary text-primary'
                  : 'border-transparent text-muted-foreground hover:text-foreground'
              }`}
            >
              {filter}
              <span className="ml-2 text-xs">
                ({filter === 'ALL' ? customers.length : customers.filter(c => 
                  filter === 'ACTIVE' ? c.status === 'ACTIVE' :
                  filter === 'VIP' ? c.membershipTier === 'VIP' :
                  filter === 'PREMIUM' ? c.membershipTier === 'PREMIUM' : true
                ).length})
              </span>
            </button>
          ))}
        </div>

        {/* Customer List */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredCustomers.map((customer) => (
            <Card key={customer.id} className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-lg font-semibold text-blue-600">
                        {getInitials(customer.name)}
                      </span>
                    </div>
                    <div>
                      <CardTitle className="text-lg">{customer.name}</CardTitle>
                      <div className="flex items-center space-x-2">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getMembershipColor(customer.membershipTier)}`}>
                          {getMembershipIcon(customer.membershipTier)}
                          <span className="ml-1">{customer.membershipTier}</span>
                        </span>
                        <span className={`w-2 h-2 rounded-full ${customer.status === 'ACTIVE' ? 'bg-green-500' : 'bg-gray-400'}`} />
                      </div>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal className="w-4 h-4" />
                  </Button>
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                {/* Contact Info */}
                <div className="space-y-2">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Mail className="w-4 h-4 mr-2" />
                    {customer.email}
                  </div>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Phone className="w-4 h-4 mr-2" />
                    {customer.phone}
                  </div>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <MapPin className="w-4 h-4 mr-2" />
                    {customer.address}
                  </div>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-600">
                      {formatCurrency(customer.totalSpent)}
                    </p>
                    <p className="text-xs text-muted-foreground">Total Spent</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-blue-600">
                      {customer.totalBookings}
                    </p>
                    <p className="text-xs text-muted-foreground">Bookings</p>
                  </div>
                </div>

                {/* Vehicles */}
                <div className="pt-2">
                  <p className="text-sm font-medium mb-2">Vehicles:</p>
                  <div className="space-y-1">
                    {customer.vehicles.slice(0, 2).map((vehicle, idx) => (
                      <div key={idx} className="flex items-center text-sm text-muted-foreground">
                        <Car className="w-3 h-3 mr-2" />
                        {vehicle.year} {vehicle.make} {vehicle.model} ({vehicle.color})
                      </div>
                    ))}
                    {customer.vehicles.length > 2 && (
                      <p className="text-xs text-muted-foreground">
                        +{customer.vehicles.length - 2} more
                      </p>
                    )}
                  </div>
                </div>

                {/* Last Visit */}
                <div className="pt-2 border-t">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Last Visit:</span>
                    <span className="text-sm font-medium">{timeAgo(customer.lastVisit)}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Loyalty Points:</span>
                    <span className="text-sm font-medium text-orange-600">
                      {customer.loyaltyPoints.toLocaleString()}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredCustomers.length === 0 && (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
                <Search className="w-8 h-8 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-2">No customers found</h3>
              <p className="text-muted-foreground text-center">
                Try adjusting your search criteria or add a new customer.
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
}