'use client';

import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Users, 
  Calendar, 
  DollarSign, 
  Car, 
  TrendingUp, 
  Clock,
  CheckCircle,
  AlertCircle,
  Camera,
  MapPin
} from 'lucide-react';

// Mock data - will be replaced with real API calls
const dashboardStats = {
  totalCustomers: 1247,
  todayBookings: 23,
  monthlyRevenue: 45600,
  activeServices: 8,
  completedToday: 18,
  inProgress: 5,
  pendingPhotos: 3,
  staffOnDuty: 12,
};

const recentBookings = [
  {
    id: 1,
    customer: '<PERSON> Doe',
    service: 'Premium Wash & Wax',
    time: '09:30',
    status: 'IN_PROGRESS',
    vehicle: 'BMW X5',
  },
  {
    id: 2,
    customer: '<PERSON>',
    service: 'Basic Wash',
    time: '10:00',
    status: 'PENDING',
    vehicle: 'Toyota Corolla',
  },
  {
    id: 3,
    customer: '<PERSON>',
    service: 'Full Detail',
    time: '10:30',
    status: 'COMPLETED',
    vehicle: 'Mercedes C-Class',
  },
];

const quickActions = [
  { name: 'New Booking', icon: Calendar, href: '/bookings/new', color: 'bg-blue-500' },
  { name: 'Photo Upload', icon: Camera, href: '/photos/upload', color: 'bg-green-500' },
  { name: 'Track Service', icon: MapPin, href: '/tracking', color: 'bg-purple-500' },
  { name: 'Add Customer', icon: Users, href: '/customers/new', color: 'bg-orange-500' },
];

function getStatusColor(status: string) {
  switch (status) {
    case 'COMPLETED': return 'text-green-600 bg-green-50 border-green-200';
    case 'IN_PROGRESS': return 'text-blue-600 bg-blue-50 border-blue-200';
    case 'PENDING': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    default: return 'text-gray-600 bg-gray-50 border-gray-200';
  }
}

export default function DashboardPage() {
  return (
    <DashboardLayout title="Dashboard" subtitle="Welcome back! Here's what's happening today.">
      <div className="p-6 space-y-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Customers</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{dashboardStats.totalCustomers.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+12%</span> from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Today's Bookings</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{dashboardStats.todayBookings}</div>
              <p className="text-xs text-muted-foreground">
                {dashboardStats.completedToday} completed, {dashboardStats.inProgress} in progress
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">R{dashboardStats.monthlyRevenue.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+8%</span> from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Staff on Duty</CardTitle>
              <Car className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{dashboardStats.staffOnDuty}</div>
              <p className="text-xs text-muted-foreground">
                All stations operational
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Recent Bookings */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Recent Bookings</CardTitle>
              <CardDescription>Today's scheduled services and their status</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentBookings.map((booking) => (
                  <div key={booking.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <Car className="w-5 h-5 text-blue-600" />
                      </div>
                      <div>
                        <p className="font-medium">{booking.customer}</p>
                        <p className="text-sm text-muted-foreground">{booking.vehicle}</p>
                        <p className="text-xs text-muted-foreground">{booking.service}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">{booking.time}</p>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(booking.status)}`}>
                        {booking.status.replace('_', ' ')}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Common tasks for efficient management</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-3">
                {quickActions.map((action) => (
                  <button
                    key={action.name}
                    className="flex flex-col items-center justify-center p-4 border rounded-lg hover:bg-muted transition-colors"
                  >
                    <div className={`w-10 h-10 ${action.color} rounded-lg flex items-center justify-center mb-2`}>
                      <action.icon className="w-5 h-5 text-white" />
                    </div>
                    <span className="text-sm font-medium text-center">{action.name}</span>
                  </button>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Status Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Services in Progress</CardTitle>
              <Clock className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{dashboardStats.inProgress}</div>
              <p className="text-xs text-muted-foreground">Currently being serviced</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed Today</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{dashboardStats.completedToday}</div>
              <p className="text-xs text-muted-foreground">Successfully finished</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Photos</CardTitle>
              <Camera className="h-4 w-4 text-orange-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{dashboardStats.pendingPhotos}</div>
              <p className="text-xs text-muted-foreground">Need documentation</p>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}