'use client';

import { useState } from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Camera,
  Upload,
  Search,
  Filter,
  Eye,
  Download,
  Calendar,
  User,
  Car,
  MapPin,
  Clock,
  CheckCircle,
  AlertTriangle,
  Trash2
} from 'lucide-react';
import { formatDateTime, timeAgo } from '@/lib/utils';

// Mock photo gallery data
const photoGallery = [
  {
    id: 1,
    bookingId: 'BK-2024-001',
    customer: '<PERSON>',
    vehicle: '2022 BMW X5 - Black',
    service: 'Premium Wash & Wax',
    step: 'Check-in Inspection',
    stepId: 1,
    photographer: '<PERSON>',
    timestamp: new Date('2024-01-15T09:30:00'),
    bay: 'Bay 2',
    photoType: 'BEFORE',
    url: '/photos/checkin-1.jpg',
    thumbnail: '/photos/thumbs/checkin-1-thumb.jpg',
    tags: ['exterior', 'front-view', 'damage-assessment'],
    notes: 'Minor scratches on front bumper noted',
    approved: true
  },
  {
    id: 2,
    bookingId: 'BK-2024-001',
    customer: 'John Doe',
    vehicle: '2022 BMW X5 - Black',
    service: 'Premium Wash & Wax',
    step: 'Foam Application',
    stepId: 3,
    photographer: 'Mike Johnson',
    timestamp: new Date('2024-01-15T10:15:00'),
    bay: 'Bay 2',
    photoType: 'PROGRESS',
    url: '/photos/foam-1.jpg',
    thumbnail: '/photos/thumbs/foam-1-thumb.jpg',
    tags: ['exterior', 'foam-coverage', 'process'],
    notes: 'Even foam coverage achieved',
    approved: true
  },
  {
    id: 3,
    bookingId: 'BK-2024-002',
    customer: 'Sarah Wilson',
    vehicle: '2021 Toyota Corolla - Silver',
    service: 'Basic Wash',
    step: 'Final Inspection',
    stepId: 6,
    photographer: 'David Smith',
    timestamp: new Date('2024-01-15T11:45:00'),
    bay: 'Bay 1',
    photoType: 'AFTER',
    url: '/photos/final-2.jpg',
    thumbnail: '/photos/thumbs/final-2-thumb.jpg',
    tags: ['exterior', 'completed', 'clean'],
    notes: 'Service completed to standard',
    approved: false
  },
  {
    id: 4,
    bookingId: 'BK-2024-003',
    customer: 'Mike Brown',
    vehicle: '2023 Mercedes C-Class - Blue',
    service: 'Full Detail',
    step: 'Interior Detail',
    stepId: 4,
    photographer: 'Alex Wilson',
    timestamp: new Date('2024-01-15T10:30:00'),
    bay: 'Bay 3',
    photoType: 'PROGRESS',
    url: '/photos/interior-3.jpg',
    thumbnail: '/photos/thumbs/interior-3-thumb.jpg',
    tags: ['interior', 'dashboard', 'cleaning'],
    notes: 'Dashboard and console detailed',
    approved: true
  },
  {
    id: 5,
    bookingId: 'BK-2024-004',
    customer: 'Lisa Davis',
    vehicle: '2020 Honda Civic - White',
    service: 'Premium Wash',
    step: 'Check-in Inspection',
    stepId: 1,
    photographer: 'Tom Rodriguez',
    timestamp: new Date('2024-01-15T08:15:00'),
    bay: 'Bay 4',
    photoType: 'BEFORE',
    url: '/photos/checkin-4.jpg',
    thumbnail: '/photos/thumbs/checkin-4-thumb.jpg',
    tags: ['exterior', 'rear-view', 'damage-assessment'],
    notes: 'Paint in excellent condition',
    approved: true
  },
  {
    id: 6,
    bookingId: 'BK-2024-004',
    customer: 'Lisa Davis',
    vehicle: '2020 Honda Civic - White',
    service: 'Premium Wash',
    step: 'Final Inspection',
    stepId: 6,
    photographer: 'Tom Rodriguez',
    timestamp: new Date('2024-01-15T09:45:00'),
    bay: 'Bay 4',
    photoType: 'AFTER',
    url: '/photos/final-4.jpg',
    thumbnail: '/photos/thumbs/final-4-thumb.jpg',
    tags: ['exterior', 'completed', 'spotless'],
    notes: 'Excellent finish achieved',
    approved: false
  }
];

function getPhotoTypeColor(type: string) {
  switch (type) {
    case 'BEFORE': return 'bg-orange-100 text-orange-700 border-orange-200';
    case 'AFTER': return 'bg-green-100 text-green-700 border-green-200';
    case 'PROGRESS': return 'bg-blue-100 text-blue-700 border-blue-200';
    default: return 'bg-gray-100 text-gray-700 border-gray-200';
  }
}

export default function PhotosPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('ALL');
  const [selectedPhoto, setSelectedPhoto] = useState(null);

  const filteredPhotos = photoGallery.filter(photo => {
    const matchesSearch = photo.customer.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         photo.bookingId.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         photo.vehicle.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesFilter = selectedFilter === 'ALL' || 
                         (selectedFilter === 'BEFORE' && photo.photoType === 'BEFORE') ||
                         (selectedFilter === 'AFTER' && photo.photoType === 'AFTER') ||
                         (selectedFilter === 'PROGRESS' && photo.photoType === 'PROGRESS') ||
                         (selectedFilter === 'PENDING' && !photo.approved);

    return matchesSearch && matchesFilter;
  });

  // Group photos by booking
  const groupedPhotos = filteredPhotos.reduce((acc, photo) => {
    if (!acc[photo.bookingId]) {
      acc[photo.bookingId] = [];
    }
    acc[photo.bookingId].push(photo);
    return acc;
  }, {});

  return (
    <DashboardLayout title="Photo Gallery" subtitle="Service documentation and quality control">
      <div className="p-6 space-y-6">
        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row gap-4 justify-between">
          <div className="flex flex-1 gap-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Search by customer, booking ID, or vehicle..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline" size="sm">
              <Filter className="w-4 h-4 mr-2" />
              Filter
            </Button>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Upload className="w-4 h-4 mr-2" />
              Upload
            </Button>
            <Button>
              <Camera className="w-4 h-4 mr-2" />
              Take Photo
            </Button>
          </div>
        </div>

        {/* Filter Tabs */}
        <div className="flex space-x-2 border-b">
          {['ALL', 'BEFORE', 'PROGRESS', 'AFTER', 'PENDING'].map((filter) => (
            <button
              key={filter}
              onClick={() => setSelectedFilter(filter)}
              className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                selectedFilter === filter
                  ? 'border-primary text-primary'
                  : 'border-transparent text-muted-foreground hover:text-foreground'
              }`}
            >
              {filter}
              <span className="ml-2 text-xs">
                ({filter === 'ALL' ? photoGallery.length : 
                  filter === 'PENDING' ? photoGallery.filter(p => !p.approved).length :
                  photoGallery.filter(p => p.photoType === filter).length})
              </span>
            </button>
          ))}
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="flex items-center justify-between p-4">
              <div>
                <p className="text-2xl font-bold">{photoGallery.length}</p>
                <p className="text-xs text-muted-foreground">Total Photos</p>
              </div>
              <Camera className="w-6 h-6 text-blue-500" />
            </CardContent>
          </Card>
          <Card>
            <CardContent className="flex items-center justify-between p-4">
              <div>
                <p className="text-2xl font-bold text-orange-600">
                  {photoGallery.filter(p => p.photoType === 'BEFORE').length}
                </p>
                <p className="text-xs text-muted-foreground">Before Photos</p>
              </div>
              <AlertTriangle className="w-6 h-6 text-orange-500" />
            </CardContent>
          </Card>
          <Card>
            <CardContent className="flex items-center justify-between p-4">
              <div>
                <p className="text-2xl font-bold text-green-600">
                  {photoGallery.filter(p => p.photoType === 'AFTER').length}
                </p>
                <p className="text-xs text-muted-foreground">After Photos</p>
              </div>
              <CheckCircle className="w-6 h-6 text-green-500" />
            </CardContent>
          </Card>
          <Card>
            <CardContent className="flex items-center justify-between p-4">
              <div>
                <p className="text-2xl font-bold text-red-600">
                  {photoGallery.filter(p => !p.approved).length}
                </p>
                <p className="text-xs text-muted-foreground">Pending Approval</p>
              </div>
              <Clock className="w-6 h-6 text-red-500" />
            </CardContent>
          </Card>
        </div>

        {/* Photo Gallery by Booking */}
        <div className="space-y-6">
          {Object.entries(groupedPhotos).map(([bookingId, photos]) => (
            <Card key={bookingId}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="flex items-center space-x-2">
                      <span>{photos[0].customer}</span>
                      <span className="text-sm font-normal text-muted-foreground">
                        ({bookingId})
                      </span>
                    </CardTitle>
                    <CardDescription className="space-y-1">
                      <div className="flex items-center text-sm">
                        <Car className="w-4 h-4 mr-2" />
                        {photos[0].vehicle} • {photos[0].service}
                      </div>
                      <div className="flex items-center text-sm">
                        <MapPin className="w-4 h-4 mr-2" />
                        {photos[0].bay}
                      </div>
                    </CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-muted-foreground">
                      {photos.length} photos
                    </span>
                    {photos.some(p => !p.approved) && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-700 border border-red-200">
                        Pending Review
                      </span>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                  {photos.map((photo) => (
                    <div key={photo.id} className="group relative">
                      <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden cursor-pointer hover:shadow-lg transition-shadow">
                        <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200">
                          <Camera className="w-8 h-8 text-gray-400" />
                        </div>
                      </div>
                      
                      {/* Photo overlay info */}
                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex flex-col justify-between p-2">
                        <div className="space-y-1">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPhotoTypeColor(photo.photoType)}`}>
                            {photo.photoType}
                          </span>
                          {!photo.approved && (
                            <div className="w-full">
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-500 text-white">
                                Pending
                              </span>
                            </div>
                          )}
                        </div>
                        
                        <div className="flex justify-between items-end">
                          <div className="text-white text-xs">
                            <p className="font-medium">{photo.step}</p>
                            <p className="text-white/70">{timeAgo(photo.timestamp)}</p>
                            <p className="text-white/70">{photo.photographer}</p>
                          </div>
                          <div className="flex space-x-1">
                            <Button size="sm" variant="ghost" className="h-6 w-6 p-0 text-white hover:bg-white/20">
                              <Eye className="w-3 h-3" />
                            </Button>
                            <Button size="sm" variant="ghost" className="h-6 w-6 p-0 text-white hover:bg-white/20">
                              <Download className="w-3 h-3" />
                            </Button>
                            <Button size="sm" variant="ghost" className="h-6 w-6 p-0 text-white hover:bg-red-500">
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                
                {/* Photo notes */}
                {photos.some(p => p.notes) && (
                  <div className="mt-4 pt-4 border-t">
                    <h4 className="text-sm font-medium mb-2">Notes:</h4>
                    <div className="space-y-1">
                      {photos.filter(p => p.notes).map((photo) => (
                        <div key={photo.id} className="text-sm text-muted-foreground">
                          <span className="font-medium">{photo.step}:</span> {photo.notes}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredPhotos.length === 0 && (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
                <Camera className="w-8 h-8 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-2">No photos found</h3>
              <p className="text-muted-foreground text-center">
                Try adjusting your search criteria or upload some photos.
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
}