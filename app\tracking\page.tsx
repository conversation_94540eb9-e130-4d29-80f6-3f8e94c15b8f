'use client';

import { useState } from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  MapPin, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Camera,
  User,
  Car,
  Droplets,
  Sparkles,
  Shield,
  Star,
  Play,
  Pause,
  Square
} from 'lucide-react';
import { formatDateTime, timeAgo } from '@/lib/utils';

// Service workflow steps for car wash
const serviceSteps = [
  {
    id: 1,
    name: 'Check-in & Inspection',
    description: 'Customer arrival, vehicle inspection, damage assessment',
    icon: Car,
    estimatedTime: '5 min',
    requiresPhoto: true,
    status: 'COMPLETED'
  },
  {
    id: 2,
    name: 'Pre-wash Rinse',
    description: 'Initial rinse to remove loose dirt and debris',
    icon: Droplets,
    estimatedTime: '10 min',
    requiresPhoto: false,
    status: 'COMPLETED'
  },
  {
    id: 3,
    name: 'Foam Application',
    description: 'Apply pre-wash foam and let it dwell',
    icon: Sparkles,
    estimatedTime: '15 min',
    requiresPhoto: true,
    status: 'IN_PROGRESS'
  },
  {
    id: 4,
    name: 'Hand Wash',
    description: 'Careful hand washing with premium products',
    icon: Shield,
    estimatedTime: '20 min',
    requiresPhoto: false,
    status: 'PENDING'
  },
  {
    id: 5,
    name: 'Rinse & Dry',
    description: 'Thorough rinse and professional drying',
    icon: Droplets,
    estimatedTime: '15 min',
    requiresPhoto: false,
    status: 'PENDING'
  },
  {
    id: 6,
    name: 'Final Inspection',
    description: 'Quality check and final photos',
    icon: Star,
    estimatedTime: '10 min',
    requiresPhoto: true,
    status: 'PENDING'
  }
];

// Active service tracking data
const activeServices = [
  {
    id: 1,
    bookingId: 'BK-2024-001',
    customer: 'John Doe',
    vehicle: '2022 BMW X5 - Black',
    service: 'Premium Wash & Wax',
    startTime: new Date('2024-01-15T09:30:00'),
    estimatedCompletion: new Date('2024-01-15T11:00:00'),
    currentStep: 3,
    assignedStaff: 'Mike Johnson',
    bay: 'Bay 2',
    progress: 45,
    photos: [
      { step: 1, url: '/photos/checkin-1.jpg', timestamp: new Date('2024-01-15T09:30:00') },
      { step: 3, url: '/photos/foam-1.jpg', timestamp: new Date('2024-01-15T10:15:00') }
    ]
  },
  {
    id: 2,
    bookingId: 'BK-2024-002',
    customer: 'Sarah Wilson',
    vehicle: '2021 Toyota Corolla - Silver',
    service: 'Basic Wash',
    startTime: new Date('2024-01-15T10:00:00'),
    estimatedCompletion: new Date('2024-01-15T10:45:00'),
    currentStep: 2,
    assignedStaff: 'David Smith',
    bay: 'Bay 1',
    progress: 25,
    photos: [
      { step: 1, url: '/photos/checkin-2.jpg', timestamp: new Date('2024-01-15T10:00:00') }
    ]
  },
  {
    id: 3,
    bookingId: 'BK-2024-003',
    customer: 'Mike Brown',
    vehicle: '2023 Mercedes C-Class - Blue',
    service: 'Full Detail',
    startTime: new Date('2024-01-15T08:30:00'),
    estimatedCompletion: new Date('2024-01-15T12:00:00'),
    currentStep: 6,
    assignedStaff: 'Alex Wilson',
    bay: 'Bay 3',
    progress: 90,
    photos: [
      { step: 1, url: '/photos/checkin-3.jpg', timestamp: new Date('2024-01-15T08:30:00') },
      { step: 3, url: '/photos/foam-3.jpg', timestamp: new Date('2024-01-15T09:45:00') },
      { step: 6, url: '/photos/final-3.jpg', timestamp: new Date('2024-01-15T11:45:00') }
    ]
  }
];

function getStepStatus(stepId: number, currentStep: number) {
  if (stepId < currentStep) return 'COMPLETED';
  if (stepId === currentStep) return 'IN_PROGRESS';
  return 'PENDING';
}

function getStatusColor(status: string) {
  switch (status) {
    case 'COMPLETED': return 'bg-green-500 text-white';
    case 'IN_PROGRESS': return 'bg-blue-500 text-white animate-pulse';
    case 'PENDING': return 'bg-gray-200 text-gray-600';
    default: return 'bg-gray-200 text-gray-600';
  }
}

function getStatusIcon(status: string) {
  switch (status) {
    case 'COMPLETED': return CheckCircle;
    case 'IN_PROGRESS': return Clock;
    case 'PENDING': return AlertCircle;
    default: return AlertCircle;
  }
}

export default function TrackingPage() {
  const [selectedService, setSelectedService] = useState(activeServices[0]);

  return (
    <DashboardLayout title="Live Service Tracking" subtitle="Monitor active services and workflow progress">
      <div className="p-6 space-y-6">
        {/* Active Services Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {activeServices.map((service) => (
            <Card 
              key={service.id} 
              className={`cursor-pointer transition-all hover:shadow-lg ${
                selectedService.id === service.id ? 'ring-2 ring-primary' : ''
              }`}
              onClick={() => setSelectedService(service)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{service.customer}</CardTitle>
                  <span className="text-xs font-mono bg-muted px-2 py-1 rounded">
                    {service.bookingId}
                  </span>
                </div>
                <CardDescription className="space-y-1">
                  <div className="flex items-center text-sm">
                    <Car className="w-4 h-4 mr-2" />
                    {service.vehicle}
                  </div>
                  <div className="flex items-center text-sm">
                    <MapPin className="w-4 h-4 mr-2" />
                    {service.bay}
                  </div>
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Progress</span>
                    <span className="text-sm font-medium">{service.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-primary h-2 rounded-full transition-all duration-300" 
                      style={{ width: `${service.progress}%` }}
                    />
                  </div>
                  <div className="flex justify-between items-center text-xs text-muted-foreground">
                    <span>Started: {timeAgo(service.startTime)}</span>
                    <span>ETA: {formatDateTime(service.estimatedCompletion).split(' ')[1]}</span>
                  </div>
                  <div className="flex items-center justify-between pt-2">
                    <div className="flex items-center">
                      <User className="w-4 h-4 mr-1" />
                      <span className="text-sm">{service.assignedStaff}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Camera className="w-4 h-4" />
                      <span className="text-sm">{service.photos.length}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Detailed Service Workflow */}
        {selectedService && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Workflow Steps */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>Service Workflow - {selectedService.customer}</CardTitle>
                  <CardDescription>
                    {selectedService.service} • Bay {selectedService.bay} • Staff: {selectedService.assignedStaff}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {serviceSteps.map((step, index) => {
                      const status = getStepStatus(step.id, selectedService.currentStep);
                      const StatusIcon = getStatusIcon(status);
                      const StepIcon = step.icon;
                      
                      return (
                        <div key={step.id} className="flex items-start space-x-4">
                          {/* Step indicator */}
                          <div className="flex flex-col items-center">
                            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${getStatusColor(status)}`}>
                              <StatusIcon className="w-5 h-5" />
                            </div>
                            {index < serviceSteps.length - 1 && (
                              <div className={`w-0.5 h-12 mt-2 ${
                                status === 'COMPLETED' ? 'bg-green-500' : 'bg-gray-200'
                              }`} />
                            )}
                          </div>
                          
                          {/* Step content */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-2">
                                <StepIcon className="w-5 h-5 text-muted-foreground" />
                                <h4 className="font-medium">{step.name}</h4>
                                {step.requiresPhoto && (
                                  <Camera className="w-4 h-4 text-blue-500" />
                                )}
                              </div>
                              <div className="flex items-center space-x-2">
                                <span className="text-sm text-muted-foreground">
                                  {step.estimatedTime}
                                </span>
                                {status === 'IN_PROGRESS' && (
                                  <Button size="sm" variant="outline">
                                    <Pause className="w-4 h-4 mr-1" />
                                    Pause
                                  </Button>
                                )}
                              </div>
                            </div>
                            <p className="text-sm text-muted-foreground mt-1">
                              {step.description}
                            </p>
                            
                            {/* Show photos for this step */}
                            {selectedService.photos.filter(photo => photo.step === step.id).length > 0 && (
                              <div className="mt-2">
                                <div className="flex items-center space-x-2">
                                  {selectedService.photos
                                    .filter(photo => photo.step === step.id)
                                    .map((photo, photoIndex) => (
                                      <div key={photoIndex} className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                                        <Camera className="w-6 h-6 text-gray-400" />
                                      </div>
                                    ))}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Service Controls & Info */}
            <div className="space-y-6">
              {/* Service Controls */}
              <Card>
                <CardHeader>
                  <CardTitle>Service Controls</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-2">
                    <Button variant="outline" size="sm">
                      <Play className="w-4 h-4 mr-2" />
                      Start
                    </Button>
                    <Button variant="outline" size="sm">
                      <Pause className="w-4 h-4 mr-2" />
                      Pause
                    </Button>
                  </div>
                  <Button className="w-full" size="sm">
                    <Camera className="w-4 h-4 mr-2" />
                    Take Photo
                  </Button>
                  <Button variant="destructive" className="w-full" size="sm">
                    <Square className="w-4 h-4 mr-2" />
                    Complete Service
                  </Button>
                </CardContent>
              </Card>

              {/* Service Timeline */}
              <Card>
                <CardHeader>
                  <CardTitle>Timeline</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Started:</span>
                      <span>{formatDateTime(selectedService.startTime)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Estimated:</span>
                      <span>{formatDateTime(selectedService.estimatedCompletion)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Duration:</span>
                      <span>
                        {Math.round((new Date().getTime() - selectedService.startTime.getTime()) / (1000 * 60))} min
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Photos Gallery */}
              <Card>
                <CardHeader>
                  <CardTitle>Service Photos ({selectedService.photos.length})</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-2">
                    {selectedService.photos.map((photo, index) => (
                      <div key={index} className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center">
                        <Camera className="w-6 h-6 text-gray-400" />
                      </div>
                    ))}
                    <button className="aspect-square border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center hover:border-primary transition-colors">
                      <Camera className="w-6 h-6 text-gray-400" />
                    </button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}