import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import bcrypt from 'bcryptjs';
import { prisma } from './db';

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error('Missing credentials');
        }

        const adminUser = await prisma.adminUser.findUnique({
          where: {
            email: credentials.email
          }
        });

        if (!adminUser || !adminUser.isActive) {
          throw new Error('Invalid credentials or account disabled');
        }

        const isPasswordValid = await bcrypt.compare(
          credentials.password,
          adminUser.password
        );

        if (!isPasswordValid) {
          throw new Error('Invalid credentials');
        }

        // Update last login
        await prisma.adminUser.update({
          where: { id: adminUser.id },
          data: { lastLogin: new Date() }
        });

        return {
          id: adminUser.id,
          email: adminUser.email,
          name: adminUser.name,
          role: adminUser.role,
        };
      }
    })
  ],
  session: {
    strategy: 'jwt',
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = (user as any).role;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!;
        session.user.role = token.role as string;
      }
      return session;
    },
  },
  pages: {
    signIn: '/login',
  },
  secret: process.env.NEXTAUTH_SECRET,
};

// IP Allowlist check
export function checkIPAllowlist(req: Request): boolean {
  const allowedIPs = process.env.ALLOWED_ADMIN_IPS?.split(',') || [];
  
  if (allowedIPs.length === 0) return true; // No restriction if not configured
  
  // Get client IP (works with various proxy setups)
  const forwarded = req.headers.get('x-forwarded-for');
  const clientIP = forwarded ? forwarded.split(',')[0] : 
                   req.headers.get('x-real-ip') || 'unknown';
                   
  console.log('Client IP:', clientIP, 'Allowed IPs:', allowedIPs);
  
  return allowedIPs.some(allowedIP => {
    if (allowedIP.includes('/')) {
      // Handle CIDR notation (basic implementation)
      const [network, bits] = allowedIP.split('/');
      // For production, use a proper IP range library
      return clientIP.startsWith(network.split('.').slice(0, Math.ceil(parseInt(bits) / 8)).join('.'));
    }
    return clientIP === allowedIP.trim();
  });
}