import { withAuth } from 'next-auth/middleware';
import { NextResponse } from 'next/server';

export default withAuth(
  function middleware(req) {
    // IP Allowlist check (optional - can be configured via environment)
    const allowedIPs = process.env.ALLOWED_ADMIN_IPS?.split(',') || [];
    
    if (allowedIPs.length > 0) {
      const forwarded = req.headers.get('x-forwarded-for');
      const clientIP = forwarded ? forwarded.split(',')[0] : req.ip || 'unknown';
      
      const isAllowed = allowedIPs.some(allowedIP => {
        if (allowedIP.includes('/')) {
          // Basic CIDR check (for production, use a proper IP library)
          const [network, bits] = allowedIP.split('/');
          return clientIP.startsWith(network.split('.').slice(0, Math.ceil(parseInt(bits) / 8)).join('.'));
        }
        return clientIP === allowedIP.trim();
      });

      if (!isAllowed) {
        console.warn(`Unauthorized access attempt from IP: ${clientIP}`);
        return NextResponse.json(
          { error: 'Access denied - IP not authorized' },
          { status: 403 }
        );
      }
    }

    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token }) => !!token,
    },
  }
);

export const config = {
  matcher: [
    '/dashboard/:path*',
    '/customers/:path*',
    '/bookings/:path*',
    '/services/:path*',
    '/staff/:path*',
    '/payments/:path*',
    '/photos/:path*',
    '/tracking/:path*',
    '/notifications/:path*',
    '/analytics/:path*',
    '/settings/:path*',
    '/api/:path*'
  ],
};