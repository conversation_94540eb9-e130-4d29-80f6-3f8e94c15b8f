{"name": "ekhaya-crm", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "prisma generate && next build", "start": "next start -p 3001", "lint": "next lint", "db:push": "prisma db push", "db:studio": "prisma studio"}, "dependencies": {"@auth/prisma-adapter": "^2.4.0", "@hookform/resolvers": "^3.3.4", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.13.0", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-table": "^8.17.3", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^3.6.0", "framer-motion": "^11.2.10", "lucide-react": "^0.395.0", "next": "14.2.28", "next-auth": "^4.24.7", "next-themes": "^0.3.0", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.51.5", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.28", "postcss": "^8", "prisma": "^6.13.0", "tailwindcss": "^3.4.1", "typescript": "^5"}}